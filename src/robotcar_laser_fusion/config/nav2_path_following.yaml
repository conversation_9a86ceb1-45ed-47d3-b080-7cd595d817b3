### Nav2 DWB动态窗口路径跟随配置文件 ###
# 专门用于固定路径跟随功能
# 控制器：DWB动态窗口方法 (Dynamic Window Approach Controller)
# 规划器：SMAC 2D规划器
# 适用场景：用户绘制路径后持续跟随该路径，具有更好的动态障碍物避障能力

# ========== 行为树导航器配置 ==========
bt_navigator:
  ros__parameters:
    use_sim_time: false
    global_frame: map
    robot_base_frame: base_footprint
    odom_topic: /odom
    bt_loop_duration: 10
    default_server_timeout: 20
    
    # 使用路径跟随专用的行为树
    default_nav_to_pose_bt_xml: "/opt/ros/humble/share/nav2_bt_navigator/behavior_trees/navigate_to_pose_w_replanning_and_recovery.xml"
    default_nav_through_poses_bt_xml: "/opt/ros/humble/share/nav2_bt_navigator/behavior_trees/navigate_through_poses_w_replanning_and_recovery.xml"
    
    # 插件配置
    plugin_lib_names:
    - nav2_compute_path_to_pose_action_bt_node
    - nav2_compute_path_through_poses_action_bt_node
    - nav2_smooth_path_action_bt_node
    - nav2_follow_path_action_bt_node
    - nav2_spin_action_bt_node
    - nav2_wait_action_bt_node
    - nav2_assisted_teleop_action_bt_node
    - nav2_back_up_action_bt_node
    - nav2_drive_on_heading_bt_node
    - nav2_clear_costmap_service_bt_node
    - nav2_is_stuck_condition_bt_node
    - nav2_goal_reached_condition_bt_node
    - nav2_goal_updated_condition_bt_node
    - nav2_globally_updated_goal_condition_bt_node
    - nav2_is_path_valid_condition_bt_node
    - nav2_initial_pose_received_condition_bt_node
    - nav2_reinitialize_global_localization_service_bt_node
    - nav2_rate_controller_bt_node
    - nav2_distance_controller_bt_node
    - nav2_speed_controller_bt_node
    - nav2_truncate_path_action_bt_node
    - nav2_truncate_path_local_action_bt_node
    - nav2_goal_updater_node_bt_node
    - nav2_recovery_node_bt_node
    - nav2_pipeline_sequence_bt_node
    - nav2_round_robin_node_bt_node
    - nav2_transform_available_condition_bt_node
    - nav2_time_expired_condition_bt_node
    - nav2_path_expiring_timer_condition
    - nav2_distance_traveled_condition_bt_node
    - nav2_single_trigger_bt_node
    - nav2_goal_updated_controller_bt_node
    - nav2_is_battery_low_condition_bt_node
    - nav2_navigate_through_poses_action_bt_node
    - nav2_navigate_to_pose_action_bt_node
    - nav2_remove_passed_goals_action_bt_node
    - nav2_planner_selector_bt_node
    - nav2_controller_selector_bt_node
    - nav2_goal_checker_selector_bt_node
    - nav2_controller_cancel_bt_node
    - nav2_path_longer_on_approach_bt_node
    - nav2_wait_cancel_bt_node
    - nav2_spin_cancel_bt_node
    - nav2_back_up_cancel_bt_node
    - nav2_assisted_teleop_cancel_bt_node
    - nav2_drive_on_heading_cancel_bt_node
    - nav2_is_battery_charging_condition_bt_node

# ========== 控制器服务器配置 ==========
# 使用DWB动态窗口控制器进行路径跟随
controller_server:
  ros__parameters:
    use_sim_time: false
    controller_frequency: 20.0
    min_x_velocity_threshold: 0.001
    min_y_velocity_threshold: 0.5
    min_theta_velocity_threshold: 0.001
    failure_tolerance: 3.0

    # 插件配置
    progress_checker_plugin: "progress_checker"
    goal_checker_plugins: ["goal_checker"]
    controller_plugins: ["FollowPath"]

    # === 进度检查器配置 ===
    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      required_movement_radius: 0.3
      movement_time_allowance: 15.0

    # === 目标检查器配置 ===
    goal_checker:
      plugin: "nav2_controller::SimpleGoalChecker"
      xy_goal_tolerance: 0.15
      yaw_goal_tolerance: 0.2
      stateful: True

    # === DWB动态窗口控制器配置 ===
    FollowPath:
      plugin: "dwb_core::DWBLocalPlanner"

      # --- 基本调试配置 ---
      debug_trajectory_details: false            # 关闭轨迹详细调试信息

      # --- 速度限制配置 ---
      min_vel_x: 0.0                            # 最小X方向速度(m/s)
      min_vel_y: 0.0                            # 最小Y方向速度(m/s) - 差分驱动为0
      max_vel_x: 0.4                            # 最大X方向速度(m/s)，与RPP期望速度一致
      max_vel_y: 0.0                            # 最大Y方向速度(m/s) - 差分驱动为0
      max_vel_theta: 1.0                        # 最大角速度(rad/s)

      # --- 速度范围配置 ---
      min_speed_xy: 0.0                         # 最小线速度(m/s)
      max_speed_xy: 0.4                         # 最大线速度(m/s)
      min_speed_theta: 0.0                      # 最小角速度(rad/s)

      # --- 加速度限制配置 ---
      acc_lim_x: 2.0                            # X方向加速度限制(m/s²)
      acc_lim_y: 0.0                            # Y方向加速度限制(m/s²) - 差分驱动为0
      acc_lim_theta: 2.0                        # 角加速度限制(rad/s²)，与RPP一致
      decel_lim_x: -2.0                         # X方向减速度限制(m/s²)
      decel_lim_y: 0.0                          # Y方向减速度限制(m/s²)
      decel_lim_theta: -2.0                     # 角减速度限制(rad/s²)

      # --- 轨迹采样配置 ---
      vx_samples: 20                            # X方向速度采样数
      vy_samples: 1                             # Y方向速度采样数 - 差分驱动为1
      vtheta_samples: 20                        # 角速度采样数

      # --- 轨迹仿真配置 ---
      sim_time: 1.5                             # 轨迹仿真时间(s)
      linear_granularity: 0.05                  # 线性粒度(m)
      angular_granularity: 0.025                # 角度粒度(rad)

      # --- 其他配置 ---
      transform_tolerance: 0.1                  # TF变换容差(s)，与RPP一致
      xy_goal_tolerance: 0.15                   # XY目标容差(m)，与goal_checker一致
      trans_stopped_velocity: 0.25              # 认为停止的平移速度阈值(m/s)
      short_circuit_trajectory_evaluation: true # 启用轨迹评估短路优化
      limit_vel_cmd_in_traj: false              # 不在轨迹中限制速度命令
      stateful: true                            # 启用状态保持

      # --- 轨迹评价函数配置 ---
      critics: ["RotateToGoal", "Oscillation", "BaseObstacle", "GoalAlign", "PathAlign", "PathDist", "GoalDist"]

      # 基础障碍物评价函数 - 避障权重
      BaseObstacle.scale: 0.02

      # 路径对齐评价函数 - 保持路径跟随
      PathAlign.scale: 32.0
      PathAlign.forward_point_distance: 0.1

      # 目标对齐评价函数 - 朝向目标
      GoalAlign.scale: 24.0
      GoalAlign.forward_point_distance: 0.1

      # 路径距离评价函数 - 靠近路径
      PathDist.scale: 32.0

      # 目标距离评价函数 - 靠近目标
      GoalDist.scale: 24.0

      # 旋转到目标评价函数 - 平滑旋转
      RotateToGoal.scale: 32.0
      RotateToGoal.slowing_factor: 5.0
      RotateToGoal.lookahead_time: -1.0

# ========== 局部代价地图配置 ==========
local_costmap:
  local_costmap:
    ros__parameters:
      update_frequency: 20.0
      publish_frequency: 20.0
      global_frame: odom
      robot_base_frame: base_footprint
      transform_timeout: 1.0
      use_sim_time: false
      rolling_window: true
      
      width: 3
      height: 3
      resolution: 0.03
      
      footprint: "[[0.35, 0.28], [0.35, -0.28], [-0.35, -0.28], [-0.35, 0.28]]"
      
      plugins: ["static_layer", "obstacle_layer", "inflation_layer"]
      
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True
        enabled: True
        subscribe_to_updates: True
        transform_tolerance: 1.0
      
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        observation_sources: scan
        scan:
          topic: /scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 3.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.5
          obstacle_min_range: 0.0
      
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        enabled: True
        cost_scaling_factor: 3.0
        inflation_radius: 0.55

# ========== 全局代价地图配置 ==========
global_costmap:
  global_costmap:
    ros__parameters:
      update_frequency: 10.0
      publish_frequency: 10.0
      global_frame: map
      robot_base_frame: base_footprint
      transform_timeout: 1.0
      use_sim_time: false
      
      footprint: "[[0.35, 0.28], [0.35, -0.28], [-0.35, -0.28], [-0.35, 0.28]]"
      resolution: 0.03
      track_unknown_space: false
      
      plugins: ["static_layer", "obstacle_layer", "inflation_layer"]
      
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True
        enabled: True
        subscribe_to_updates: True
        transform_tolerance: 1.0
      
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        observation_sources: scan
        scan:
          topic: /scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 3.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.5
          obstacle_min_range: 0.0
      
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        enabled: True
        cost_scaling_factor: 3.0
        inflation_radius: 0.65
      
      always_send_full_costmap: True

# ========== 地图服务器配置 ==========
map_server:
  ros__parameters:
    use_sim_time: false
    yaml_filename: ""

# ========== 规划器服务器配置 ==========
planner_server:
  ros__parameters:
    expected_planner_frequency: 10.0
    use_sim_time: false
    planner_plugins: ["GridBased"]
    
    # === SMAC 2D规划器配置 ===
    GridBased:
      plugin: "nav2_smac_planner/SmacPlanner2D"
      tolerance: 0.1
      downsample_costmap: false
      downsampling_factor: 1
      allow_unknown: false
      max_iterations: 1000000
      max_on_approach_iterations: 1000
      max_planning_time: 3.0
      motion_model_for_search: "MOORE"
      angle_quantization_bins: 72
      cost_penalty: 2.0
      use_final_approach_orientation: false
      smooth_path: true

# ========== 路径平滑器服务器配置 ==========
smoother_server:
  ros__parameters:
    use_sim_time: false
    smoother_plugins: ["simple_smoother"]
    
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"
      tolerance: 1.0e-10
      max_its: 1000
      do_refinement: true

# ========== 行为服务器配置 ==========
behavior_server:
  ros__parameters:
    costmap_topic: local_costmap/costmap
    footprint_topic: local_costmap/published_footprint
    cycle_frequency: 10.0
    behavior_plugins: ["spin", "backup", "drive_on_heading", "wait"]
    
    spin:
      plugin: "nav2_behaviors/Spin"
    backup:
      plugin: "nav2_behaviors/BackUp"
    drive_on_heading:
      plugin: "nav2_behaviors/DriveOnHeading"
    wait:
      plugin: "nav2_behaviors/Wait"
    
    global_frame: odom
    robot_base_frame: base_footprint
    transform_tolerance: 0.5
    use_sim_time: false
    simulate_ahead_time: 1.0
    max_rotational_vel: 0.4
    min_rotational_vel: 0.2
    rotational_acc_lim: 0.6

# ========== 速度平滑器配置 ==========
velocity_smoother:
  ros__parameters:
    use_sim_time: false
    smoothing_frequency: 20.0
    scale_velocities: false
    feedback: "OPEN_LOOP"
    
    max_velocity: [0.5, 0.0, 1.0]
    min_velocity: [0.0, 0.0, -1.0]
    max_accel: [0.4, 0.0, 0.8]
    max_decel: [-0.4, 0.0, -0.8]
    
    odom_topic: "odom"
    odom_duration: 0.1
    deadband_velocity: [0.0, 0.0, 0.0]
    velocity_timeout: 1.0

# ========== 路径点跟随器配置 ==========
waypoint_follower:
  ros__parameters:
    use_sim_time: false
    loop_rate: 20
    stop_on_failure: false
    waypoint_task_executor_plugin: "wait_at_waypoint"
    
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True
      waypoint_pause_duration: 0

# ========== 生命周期管理器配置 ==========
lifecycle_manager:
  ros__parameters:
    use_sim_time: false
    autostart: true
    node_names: ['controller_server', 'smoother_server', 'planner_server', 'behavior_server', 'bt_navigator', 'waypoint_follower', 'velocity_smoother']
