

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction, ExecuteProcess
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch.conditions import IfCondition
from launch_ros.actions import Node


def generate_launch_description():

    pkg_robotcar_fusion = get_package_share_directory('robotcar_laser_fusion')
    pkg_robotcar_nav = get_package_share_directory('robotcar_nav')

    # 启动参数
    use_sim_time = LaunchConfiguration('use_sim_time')
    start_rviz = LaunchConfiguration('start_rviz')
    use_ekf = LaunchConfiguration('use_ekf')
    map_file = LaunchConfiguration('map_file')
    namespace = LaunchConfiguration('namespace')

    # === 1. 启动基础硬件和双雷达融合系统 ===
    fusion_bringup_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(pkg_robotcar_fusion, 'launch', 'fusion_01_bringup.launch.py')
        ),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'namespace': namespace,
        }.items()
    )

    # === 2. 启动EKF传感器融合（可选） ===
    ekf_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(pkg_robotcar_fusion, 'launch', 'fusion_02_ekf.launch.py')
        ),
        condition=IfCondition(use_ekf),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'namespace': namespace,
        }.items()
    )

    # === 3. 启动Cartographer定位系统 ===
    cartographer_config_dir = PathJoinSubstitution([pkg_robotcar_nav, 'config'])
    cartographer_config_basename = 'cartographer_localization.lua'
    rviz_config_file = PathJoinSubstitution([pkg_robotcar_nav, 'rviz', 'rviz.rviz'])

    cartographer_localization_node = TimerAction(
        period=5.0,  # 等待融合系统完全启动
        actions=[
            Node(
                package='cartographer_ros',
                executable='cartographer_node',
                name='cartographer_node',
                namespace=namespace,
                output='screen',
                parameters=[
                    {'use_sim_time': use_sim_time},
                ],
                arguments=[
                    '-configuration_directory', cartographer_config_dir,
                    '-configuration_basename', cartographer_config_basename,
                    '-load_state_filename', map_file
                ],
                remappings=[
                    ('scan', 'merged'),  # 使用融合后的激光雷达数据
                    ('imu', 'imu'),
                    ('odom', 'diff_drive_controller/odom'),
                ]
            )
        ]
    )

    # === 4. 启动Cartographer占用栅格节点 ===
    occupancy_grid_node = TimerAction(
        period=6.0,  # 等待Cartographer节点启动
        actions=[
            Node(
                package='cartographer_ros',
                executable='cartographer_occupancy_grid_node',
                name='cartographer_occupancy_grid_node',
                namespace=namespace,
                output='screen',
                parameters=[{'use_sim_time': use_sim_time}],
                arguments=['-resolution', '0.03', '-publish_period_sec', '1.0']
            )
        ]
    )

    # === 5. 启动Nav2导航系统（使用极简DWB配置）===
    nav2_config_file = PathJoinSubstitution([pkg_robotcar_nav, 'config', 'nav2_minimal_dwb.yaml'])

    nav2_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(get_package_share_directory('nav2_bringup'), 'launch', 'navigation_launch.py')
        ),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'params_file': nav2_config_file,
            'namespace': 'chassis',  # 强制使用chassis命名空间
            'autostart': 'true',  # 强制自动启动
        }.items()
    )

    # === 6. 启动RViz可视化 ===
    navigation_rviz_node = TimerAction(
        period=8.0,  # 等待所有节点启动
        actions=[
            ExecuteProcess(
                cmd=['rviz2', '-d', rviz_config_file],
                output='screen',
                condition=IfCondition(start_rviz),
                additional_env={'DISPLAY': ':0'}
            )
        ]
    )
    # === 7. 启动cmd_vel话题重映射节点 ===
    cmd_vel_relay_node = TimerAction(
        period=1.0,  # 等待Nav2启动
        actions=[
            Node(
                package='topic_tools',
                executable='relay',
                name='cmd_vel_relay',
                output='screen',
                arguments=['cmd_vel', 'diff_drive_controller/cmd_vel_unstamped'],
                parameters=[{'use_sim_time': use_sim_time}],
                namespace=namespace
            )
        ]
    )
    return LaunchDescription([
        # === 启动参数声明 ===
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='False',  # 修复：统一使用Python布尔值格式
            description='Use simulation time if true'
        ),

        DeclareLaunchArgument(
            'start_rviz',
            default_value='False',  # 默认不启动RViz
            description='Start RViz for navigation visualization'
        ),

        DeclareLaunchArgument(
            'use_ekf',
            default_value='true',
            description='Use EKF for sensor fusion'
        ),

        DeclareLaunchArgument(
            'map_file',
            default_value='',
            description='Path to the map file for navigation (.pbstream file)'
        ),

        DeclareLaunchArgument(
            'namespace',
            default_value='chassis',
            description='ROS2 namespace for multi-robot isolation'
        ),

        # === 系统启动 ===
        fusion_bringup_launch,          # 基础硬件和双雷达融合
        ekf_launch,                     # EKF传感器融合（可选）
        cartographer_localization_node, # Cartographer定位
        occupancy_grid_node,            # Cartographer占用栅格节点
        nav2_launch,                    # Nav2导航系统
        cmd_vel_relay_node,             # cmd_vel话题重映射
        navigation_rviz_node,           # RViz可视化
    ])
